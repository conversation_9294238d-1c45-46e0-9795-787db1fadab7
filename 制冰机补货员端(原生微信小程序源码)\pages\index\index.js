// pages/index/index.js
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    statusBar: app.globalData.statusBar,
    customBar: app.globalData.customBar,
    custom: app.globalData.custom,
    info: {},
    imgUrl: 'https://ice.sdbaocheng.com',
    isLog: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录？',
      success: function (res) {
        if (res.confirm) {
          app.removeCache('token')
          wx.navigateTo({
            url: '/pages/index/loding',
          })
        }
      },
    })
  },
  // 扫码
  getScanCode() {
    wx.scanCode({
      onlyFromCamera: true,
      success(res) {
        console.log(res)
        var str = res.result
        // https://xcx.zilingnewretail.com/machine?machine-no=2022051303119
        var result = str.split('=')
        wx.navigateTo({
          // 测试 记得改
          url: '/pages/index/scancode?code=' + result[result.length - 1],
        })
      }
    })
  },

  // 登录
  getLog() {
    if (!app.isLogin()) {
      return;
    }
  },
  goUrl(e){
    wx.navigateTo({
      url: e.currentTarget.dataset.url,
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 登录状态
    if (wx.getStorageSync("token")) {
      this.setData({
        isLog: true,
      })
      this.loadData()
    }
  },
  loadData() {
    app.get('user/index').then(res => {
      if (res.code != 1) {
        return
      }
      this.setData({
        info: res.data
      })
    }).catch(err => {

    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

})