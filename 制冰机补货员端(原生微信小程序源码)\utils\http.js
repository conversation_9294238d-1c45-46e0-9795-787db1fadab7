/**
 *  这是一个小程序封装的的全局请求方式
 *  Created by gaobo on 2021/03/06
 */
// 引入缓存插件
var cache = require('./cache.js') // 缓存方法
var util = require('./util.js') // 公用插件方法
// 地址
const url = {
	dev: `https://ice.sdbaocheng.com/replenish/`,
};
const apiUrl = url.dev;
const imgUrl = 'https://ice.sdbaocheng.com';

// token 
let token = ""
// 公共参数 
//  api      请求方式/路由
//  params   参数
const httpTokenRequest = (opts, params) => {
	if (!params) params = {}
	// 公共参数
		token = cache.fetchCache('token') || ""
	let promise = new Promise(function (resolve, reject) {
		wx.request({
			url: apiUrl + opts.url, //仅为示例，并非真实的接口地址
			data: params, // 参数
			method: opts.method, // 请求方式
			header: {
				'token': token,
			},
			dataType: 'json',
			responseType: 'text',
			success(res) {
				// 拦截500
				if (res.data.code == 500) {
					util.showToast("服务器繁忙")
					return
				}
				// 未登录
				if (res.data.code == 401) {
					cache.removeCache('token') 
					wx.navigateTo({
						url: '/pages/index/loding'
					})
					return 
				}
				if (res.data.code != 1) {
					util.showToast(res.data.msg)
				}
				resolve(res.data)
			},
			fail(res) {
				console.log(res)
				reject(res.data)
			},
			complete(res) {

			}
		})

	})
	return promise

}



// POST请求
const optsPost = {
	method: "POST",
	url: ''
}
// GET请求
const optsGet = {
	method: "GET",
	url: ''
}
// 请求方式
// post
let post = (url, data) => {
	try {
		optsPost.url = url
		return httpTokenRequest(optsPost, data)
	} catch (e) {
		console.log(e)
		//TODO handle the exception
	}
}
// get
let get = (url, data) => {
	try {
		optsGet.url = url
		return httpTokenRequest(optsGet, data)
	} catch (e) {
		console.log(e)
		//TODO handle the exception
	}
}
module.exports = {
	httpTokenRequest,
	apiUrl,
	imgUrl,
	/**
	 * 写需求请求的接口
	 */
	// 接口请求
	post,
	get,


}